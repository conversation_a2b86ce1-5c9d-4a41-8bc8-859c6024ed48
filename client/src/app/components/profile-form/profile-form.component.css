.form-group__field input:not([type=radio]),
.form-group__field select {
  outline: none;
  padding: 13px 25px;
  /* max-width: 280px; */
  width: 100%;
  height: 50px;
  background: #fff;
  position: sticky;
  z-index: 1;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0;
  color: var(--font-color1);
  border: 1px solid #D19036;
  border-radius: 15px;
  background: transparent;
}

.form-group__field select {
  padding: 0px
}

.input-wrap {
  position: relative;
  height: 50px;
}

.input-wrap:has(textarea) {
  position: relative;
  height: 100px;
  border: 1px solid #D19036;
  background: transparent;
  border-radius: 15px;
  overflow: hidden;
}

.input-wrap textarea {
  height: 90px;
  overflow-y: auto;
  margin: 5px 0;
  background: transparent;
  border: none !important;
  border-radius: 15px;
  outline: none;
  position: sticky;
  padding: 8px 25px !important;
  width: 99%;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0;
  color: var(--font-color1);
  resize: none;
}

.input-wrap img {
  position: absolute;
  width: 100%;
  height: 50px;
}

.input-wrap img.textarea-bg {
  height: 100px;
}

.divider {
  background-image: var(--lib-after);
  width: 100%;
  height: 3px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin: 60px auto 80px;
  max-width: 1200px;
}

.form-group__field label {
  font-family: Prata;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  letter-spacing: 0;
  color: var(--font-color1);
  /* padding-bottom: 6px; */
}

.form-group__title {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 28px;
  letter-spacing: 0;
  padding-bottom: 35px;
  color: var(--font-color1);
}

.form-group__fields {
  display: flex;
  flex-wrap: wrap;
  row-gap: 20px;
  column-gap: 30px;
}

.form-group__fields>.form-group__field {
  flex-basis: calc(33% - 20px);
  max-width: calc(33% - 20px);
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.spiritual-path-form .form-group__fields>.form-group__field {
  flex-basis: calc(50% - 15px);
  max-width: calc(50% - 15px);
}

.profile-avatar {
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
}

.send_button,
.forms-nav-button {
  width: 234px;
  height: 50px;
  padding: 0;
  position: relative;
  margin: 60px auto 35px !important;
}

.send_button .btn-backdrop-img,
.forms-nav-button .btn-backdrop-img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
}

.send_button .btn-label,
.forms-nav-button .btn-label {
  margin: 0 auto;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: middle;
  padding: 14px 25px;
  color: var(--font-color1);
}

.forms-nav-button {
  margin: 0px auto !important;
}

.form-wide .form-group__fields>div {
  flex-basis: 100%;
}

.form-wide .form-group__fields input,
.form-wide select {
  width: 100%;
  max-width: 100%;
}

.form-radio {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.form-radio input {
  margin-right: 10px;
}

.not-auth {
  background: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  margin-top: 80px;
}

.custom-radio-group {
  margin-top: 21px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.custom-radio {
  position: relative;
  display: flex;
  align-items: center;
}

.custom-radio .custom-radio-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.custom-radio .custom-radio-label {
  display: flex !important;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0;
  color: var(--font-color1);
}

.custom-radio-button {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 32px;
  margin-right: 11px;
  background: transparent;
  border: 1px solid #D19036;
  border-radius: 50%;
}

.custom-radio-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 18px;
  height: 18px;
  background-image: url('../../../assets/images/radio-circle.webp');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.custom-radio-input:checked+.custom-radio-label .custom-radio-button::after {
  opacity: 1;
}

.select-wrapper {
  position: relative;
}

.dropdown-btn {
  border: 1px solid #D19036;
  border-radius: 15px;
  background: transparent;
  width: 100%;
  height: 50px;
  padding: 11px 20px;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  color: var(--font-color1);
  text-align: left;
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.dropdown-btn .txt_hd {
  white-space: nowrap;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-btn::after {
  content: "";
  background-image: var(--ar);
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  width: 10px;
  height: 6px;
  position: absolute;
  right: 15px;
}

.custom-dropdown {
  position: relative;
  height: fit-content;
}

.dropdown-content {
  /* display: none; */
  border: 1px solid #D19036;
  border-radius: 15px;
  background: #fff;
  z-index: 10;
  padding: 15px 0;
  overflow: hidden;
  width: 100%;
  position: absolute;
  margin-top: -1px;
}

.dropdown-content-inner {
  height: 180px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--book_about);
    border-radius: 5px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--text-color);
  }

  &::-webkit-scrollbar:horizontal {
    height: 6px;
  }

  &::-webkit-scrollbar-thumb:horizontal {
    border-radius: 5px;
  }
}

/* .custom-dropdown:hover .dropdown-content,
.custom-dropdown .dropdown-content.open {
  display: block;
} */

.dropdown-item {
  padding: 11px 25px;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  color: var(--font-color1);
  cursor: pointer;
}

.dropdown-item:hover {
  background: var(--selection);
}

.dropdown-item.active {
  background: var(--selection);
}


.date-input-wrap {
  position: relative;
}

/* Hide the native date input */
.native-date-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

/* Style the formatted display to look like an input */
.formatted-date-display {
  width: 100%;
  height: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  background: var(--input-bg, #fff);
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
  min-height: 40px;
  box-sizing: border-box;
}

.formatted-date-display:hover {
  border-color: var(--primary-color, #007bff);
}

.formatted-date-display:focus-within {
  outline: 2px solid var(--primary-color, #007bff);
  outline-offset: 2px;
}

.date-text {
  flex: 1;
  color: var(--text-color, #333);
  font-size: 14px;
}

.date-text.placeholder {
  color: var(--placeholder-color, #999);
}

.custom-calendar-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 1;
}

.error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.form-footer {
  padding-top: 120px;
}

.processing-personal-data,
.processing-personal-data-link {
  font-family: Prata;
  font-weight: 400;
  font-size: 15px;
  line-height: 20px;
  letter-spacing: 0;
  text-align: center;
  color: var(--font-color1);
}

.processing-personal-data {
  margin-bottom: 4px;
}

.processing-personal-data-link {
  border-bottom: 1px solid var(--font-color1);
}

.forms-nav-buttons {
  display: none;
}

.questionnaire-sent-container {
  padding: 68px 32px 120px;
  max-width: 910px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

.questionnaire-sent-title {
  font-family: BeaumarchaisC;
  font-weight: 400;
  font-size: 60px;
  line-height: 36px;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  color: var(--font-color1);
}

.questionnaire-sent-description {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 28px;
  letter-spacing: 0;
  text-align: center;
  color: var(--font-color1);
}


.questionnaire-sent-actions {
  display: flex;
  gap: 60px;
  padding-top: 30px;
}

.questionnaire-sent-button {
  width: 300px;
  height: 50px;
  padding: 0;
  position: relative;
}

.questionnaire-sent-button .btn-label {
  margin: 0 auto;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: middle;
  padding: 14px 25px;
  color: var(--font-color1);
}

.questionnaire-sent-button .btn-backdrop-img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
}


@media (max-width: 1250px) {
  .form-group {
    display: none;
    max-width: 768px;
    margin: 0 auto;
  }

  .form-group.active-form {
    display: block;
  }

  .form-footer {
    display: none;
    padding-top: 70px;
  }

  .form-footer.active-footer {
    display: flex;
  }

  .form-footer.active-footer .forms-nav-buttons {
    margin: 0 auto;
  }

  .divider {
    display: none;
  }

  .form-group__fields {
    row-gap: 12px;
    column-gap: 20px;
  }

  .form-group__fields>.form-group__field {
    flex-basis: calc(50% - 10px);
    max-width: calc(50% - 10px);
    justify-content: flex-end;
  }

  .spiritual-path-form .form-group__fields>.form-group__field {
    flex-basis: calc(50% - 10px);
    max-width: calc(50% - 10px);
  }

  .form-group__title {
    font-size: 20px;
    line-height: 20px;
  }

  .form-group__field label {
    font-size: 14px;
    line-height: 14px;
  }

  .form-group__field input:not([type=radio]),
  .form-group__field select,
  .dropdown-btn {
    padding: 11px 15px;
    height: 40px;
    font-size: 18px;
    line-height: 20px;
    border-radius: 10px;
  }

  textarea {
    font-size: 18px;
    line-height: 20px;
  }

  .dropdown-item {
    padding: 11px 15px;
    font-size: 18px;
    line-height: 20px;
  }

  .input-wrap {
    height: 40px;
  }

  .custom-calendar-icon svg {
    zoom: 0.8;
  }

  .input-wrap:has(textarea) {
    height: 90px;
  }

  .input-wrap textarea {
    height: 80px;
    border-radius: 10px;
    padding: 8px 15px !important;
    font-size: 18px;
    line-height: 20px;
  }

  .forms-nav-buttons {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 70px 0 0;
  }

  .form-group.feedback-form .form-group__fields {
    row-gap: 41px;
  }

  .custom-radio-group {
    margin-top: 10px;
  }

  .processing-personal-data,
  .processing-personal-data-link {
    font-size: 11px;
    line-height: 13px;
  }

  .send_button {
    margin: 20px auto !important;
  }
}

@media (max-width: 920px) {
  .questionnaire-sent-container {
    padding: 100px 32px 120px;
    max-width: 558px;
    gap: 30px;
  }

  .questionnaire-sent-title {
    font-size: 34px;
    line-height: 34px;
  }

  .questionnaire-sent-description {
    font-size: 16px;
    line-height: 21px;
  }


  .questionnaire-sent-actions {
    flex-direction: column;
    gap: 20px;
    padding-top: 40px;
  }

  .questionnaire-sent-button .btn-label {
    font-size: 18px;
    line-height: 20px;
  }
}

@media (max-width: 860px) {
  .form-group {
    max-width: 720px;
    padding: 0 10px;
  }
}

@media (max-width: 720px) {
  .form-group {
    max-width: 346px;
    padding: 0;
  }

  .form-group__fields {
    padding: 0 8px;
  }

  .form-group__fields>.form-group__field {
    flex-basis: 100%;
    max-width: 100%;
  }

  .spiritual-path-form .form-group__fields>.form-group__field {
    flex-basis: 100%;
    max-width: 100%;
  }

  .form-group__title {
    text-align: center;
    padding-bottom: 40px;
  }

  .form-group__field label {
    font-size: 11px;
    line-height: 14px;
  }

  .form-group__field input:not([type=radio]),
  .form-group__field select,
  .dropdown-btn {
    height: 36px;
    font-size: 14px;
    line-height: 16px;
  }

  .dropdown-item {
    font-size: 14px;
    line-height: 16px;
  }

  .input-wrap {
    height: 36px;
  }

  .input-wrap:has(textarea) {
    height: 78px;
  }

  .form-group.feedback-form .form-group__fields {
    row-gap: 35px;
  }

  .input-wrap textarea {
    height: 68px;
    padding: 8px 15px !important;
    font-size: 14px;
    line-height: 14px;
  }

  .custom-radio-group {
    margin-top: 6px;
    gap: 14px;
  }

  .custom-radio-button {
    zoom: 0.81;
    margin-right: 15px;
  }

  .custom-radio .custom-radio-label {
    font-size: 17px;
    line-height: 17px;
  }

  .custom-radio .custom-radio-label .radio-value {
    padding-top: 2px;
  }

  .send_button,
  .forms-nav-button,
  .questionnaire-sent-button {
    zoom: 0.9;
  }
}