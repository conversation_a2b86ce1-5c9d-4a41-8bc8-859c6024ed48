import {Component, inject, Input} from '@angular/core';
import {FormBuilder,  FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {CommonModule} from "@angular/common";
import {ProfileService} from "@/services/profile.service";
import {environment} from "@/env/environment";
import {FileService} from "@/services/file.service";
import {Router} from "@angular/router";
import { countriesData } from './countries.var';
import { ToasterService } from '@/services/toaster.service';

enum formsSectionType {
  BASIC_INFORMATION = 'Основная информация',
  SPIRITUAL_PATH = 'Духовный путь',
  EDUCATION = 'Образование',
  FEEDBACK  = 'Обратная связь',
}

@Component({
  selector: 'ProfileForm',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
  ],
  templateUrl: './profile-form.component.html',
  styleUrl: './profile-form.component.css'
})
export class ProfileFormComponent {
  @Input() isProfile: boolean = false;
  fb = inject(FormBuilder);
  toasterService = inject(ToasterService);
  profileService = inject(ProfileService);
  fileService = inject(FileService);
  router = inject(Router);
  educations: string[] = [
    'не выбрано',
    'среднее',
    'среднее-специальное',
    'неоконченное высшее',
    'высшее'
  ];

  dropdownOpenStates: { [key: string]: boolean } = {
    country: false,
    statuses: false,
    dpGoal: false,
    education: false
  };
  dropdownSelectedValues: { [key: string]: string } = {
    country: '',
    statuses: '',
    dpGoal: '',
    education: this.educations[0]
  };
  formsSectionType = formsSectionType;
  formsSections: string[] = Object.values(formsSectionType);
  selectedMobileForm = this.formsSections[0];
  questionnaireSentState: boolean = false;

  goalsPractice: string[] = [
    'Гость (познакомиться с традицией)',
    'Принятие через вашу школу индуизма как своей религии',
    'Грихастха (стать учеником Гуру с минимальным количеством обетов)',
    'Карма-санньяса (стать учеником Гуру с более строгими обетами)',
    'Пурна-санньяса (стать монахом)',
    'Ванапрастха (статус отшельников, принимаемый после 50 лет с супругой или одиночно)'
  ];

  statuses: any = []
  form: any = this.fb.group({
    id: [null],
    firstName: [null],
    lastName: [null],
    middleName: [null],
    spiritualName: [null],
    email: [null, [Validators.required, Validators.email]],
    phone: [null],
    telegram: [null],
    avatar: [null],
    statuses: [[]],
    birthDate: [null],
    country: [null],
    city: [null],
    address: [null],
    language: [null],
    dpDate: [null],
    dpLevel: [null],
    dpPractice: [null],
    dpEvents: [null],
    dpMeet: [null],
    dpGoal: [null],
    education: [this.educations[0]],
    health: [null],
    speciality: [null],
    profession: [null],
    skills: [null],
    service: [null],
    supportInstructor: [null],
    supportConsultation: [null],
    supportCorrespondence: [null],
    agree: [false], //, [Validators.requiredTrue]
  })

  ngOnInit() {
    this.profileService.getProfile().subscribe({
      next: profile => {
         // Keep dates in ISO format for the form controls
         this.form.patchValue(profile);

         if(this.form.get('country')?.value)
          this.selectDrpdValue(this.form.get('country')?.value, countriesData.find(country => country.iso_code2 === this.form.get('country')?.value)?.name_ru || '', 'country');

         if(this.form.get('dpGoal')?.value)
          this.selectDrpdValue(this.form.get('dpGoal')?.value, this.form.get('dpGoal')?.value, 'dpGoal');

         if(this.form.get('education')?.value)
          this.selectDrpdValue(this.form.get('education')?.value, this.form.get('education')?.value, 'education');

          this.profileService.getStatuses().subscribe(
            statuses => {
              this.statuses = statuses;
              if(this.form.get('statuses')?.value[0])
                this.selectDrpdValue(this.form.get('statuses')?.value[0], this.statuses.find((status: any) => status.value === this.form.get('statuses')?.value[0])?.label || '', 'statuses')
            }
          );

      },
      error: () => {
        // Handle error silently or add proper error handling
      }
    })

  }

  uploadAvatar(e: Event) {
    const files = (e.target as HTMLInputElement).files!
    this.fileService.upload(files, 'avatar').subscribe((res: any) => this.form.value.avatar = res[0])
  }

  saveForm() {
    if (!this.isProfile) {
      this.questionnaireSentState = true;
    } else {
      // Dates are already in ISO format, so we can send the form directly
      this.profileService.update(this.form).subscribe({
      next: () => {
        this.toasterService.showToast('Данные успешно сохранены.', 'success', 'bottom-middle', 3000);
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })

    }
  }

  toAuthPage() {
    this.router.navigate(['/ru/signin'], {queryParams: {return: '/ru/anketa'}});
  }

  selectDrpdValue(value: string, visibleValue: string, formName: string): void {
    this.form.get(formName).patchValue(value);
    this.dropdownOpenStates[formName] = false;
    this.dropdownSelectedValues[formName] = visibleValue;
  }

  toggleDrpd(key: string) {
    this.dropdownOpenStates[key] = !this.dropdownOpenStates[key];
  }

  protected readonly environment = environment;

  countries = countriesData;

  goToPrevForm(): void {
    const currentIndex = this.formsSections.indexOf(this.selectedMobileForm);
    if (currentIndex > 0) {
      this.selectedMobileForm = this.formsSections[currentIndex - 1];
      this.scrollToPosition();
    }
  }
  
  goToNextForm(): void {
    const currentIndex = this.formsSections.indexOf(this.selectedMobileForm);
    if (currentIndex < this.formsSections.length - 1) {
      this.selectedMobileForm = this.formsSections[currentIndex + 1];
      this.scrollToPosition();
    }
  }
  
  private scrollToPosition(): void {
    const scrollPosition = window.innerWidth > 768 ? 400 : 200;
    window.scrollTo({ top: scrollPosition, behavior: 'smooth' });
  }

  // Date utility functions for consistent formatting
  private formatDateForDisplay(isoDate: string | null): string {
    if (!isoDate) return '';
    try {
      const date = new Date(isoDate);
      if (isNaN(date.getTime())) return '';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear().toString();

      return `${day}/${month}/${year}`;
    } catch {
      return '';
    }
  }





  getDateDisplayValue(controlName: string): string {
    const value = this.form.get(controlName)?.value;
    if (!value) return '';

    // If value is already in display format, return as is
    if (value.includes('/') && value.length === 10) {
      return value;
    }

    // If value is in ISO format, convert to display format
    return this.formatDateForDisplay(value);
  }

  // New methods for hybrid date picker approach
  onNativeDateChange(event: Event, controlName: string): void {
    const input = event.target as HTMLInputElement;
    const isoDate = input.value; // Native date input gives us ISO format (YYYY-MM-DD)

    if (isoDate) {
      // Update the form control with the ISO date
      this.form.get(controlName)?.setValue(isoDate);
    }
  }

  triggerDatePicker(controlName: string): void {
    // Find the hidden native date input and trigger its click
    const nativeInput = document.querySelector(`input[formControlName="${controlName}"]`) as HTMLInputElement;
    if (nativeInput) {
      nativeInput.focus();
      nativeInput.click();
    }
  }

  getFormattedDateDisplay(controlName: string): string {
    const value = this.form.get(controlName)?.value;
    if (!value) return '';

    // Convert ISO date to DD/MM/YYYY format for display
    return this.formatDateForDisplay(value);
  }
}
