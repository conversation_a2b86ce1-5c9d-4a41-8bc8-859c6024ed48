import {Component, inject, Input} from '@angular/core';
import {FormBuilder,  FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {CommonModule} from "@angular/common";
import {ProfileService} from "@/services/profile.service";
import {environment} from "@/env/environment";
import {FileService} from "@/services/file.service";
import {Router} from "@angular/router";
import { countriesData } from './countries.var';
import { ToasterService } from '@/services/toaster.service';

enum formsSectionType {
  BASIC_INFORMATION = 'Основная информация',
  SPIRITUAL_PATH = 'Духовный путь',
  EDUCATION = 'Образование',
  FEEDBACK  = 'Обратная связь',
}

@Component({
  selector: 'ProfileForm',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
  ],
  templateUrl: './profile-form.component.html',
  styleUrl: './profile-form.component.css'
})
export class ProfileFormComponent {
  @Input() isProfile: boolean = false;
  fb = inject(FormBuilder);
  toasterService = inject(ToasterService);
  profileService = inject(ProfileService);
  fileService = inject(FileService);
  router = inject(Router);
  educations: string[] = [
    'не выбрано',
    'среднее',
    'среднее-специальное',
    'неоконченное высшее',
    'высшее'
  ];

  dropdownOpenStates: { [key: string]: boolean } = {
    country: false,
    statuses: false,
    dpGoal: false,
    education: false
  };
  dropdownSelectedValues: { [key: string]: string } = {
    country: '',
    statuses: '',
    dpGoal: '',
    education: this.educations[0]
  };
  formsSectionType = formsSectionType;
  formsSections: string[] = Object.values(formsSectionType);
  selectedMobileForm = this.formsSections[0];
  questionnaireSentState: boolean = false;

  goalsPractice: string[] = [
    'Гость (познакомиться с традицией)',
    'Принятие через вашу школу индуизма как своей религии',
    'Грихастха (стать учеником Гуру с минимальным количеством обетов)',
    'Карма-санньяса (стать учеником Гуру с более строгими обетами)',
    'Пурна-санньяса (стать монахом)',
    'Ванапрастха (статус отшельников, принимаемый после 50 лет с супругой или одиночно)'
  ];

  statuses: any = []
  form: any = this.fb.group({
    id: [null],
    firstName: [null],
    lastName: [null],
    middleName: [null],
    spiritualName: [null],
    email: [null, [Validators.required, Validators.email]],
    phone: [null],
    telegram: [null],
    avatar: [null],
    statuses: [[]],
    birthDate: [null],
    country: [null],
    city: [null],
    address: [null],
    language: [null],
    dpDate: [null],
    dpLevel: [null],
    dpPractice: [null],
    dpEvents: [null],
    dpMeet: [null],
    dpGoal: [null],
    education: [this.educations[0]],
    health: [null],
    speciality: [null],
    profession: [null],
    skills: [null],
    service: [null],
    supportInstructor: [null],
    supportConsultation: [null],
    supportCorrespondence: [null],
    agree: [false], //, [Validators.requiredTrue]
  })

  ngOnInit() {
    this.profileService.getProfile().subscribe({
      next: profile => {
         // Convert ISO dates to display format before patching the form
         const profileWithFormattedDates = { ...profile };
         if (profileWithFormattedDates.birthDate) {
           profileWithFormattedDates.birthDate = this.formatDateForDisplay(profileWithFormattedDates.birthDate);
         }
         if (profileWithFormattedDates.dpDate) {
           profileWithFormattedDates.dpDate = this.formatDateForDisplay(profileWithFormattedDates.dpDate);
         }

         this.form.patchValue(profileWithFormattedDates);

         if(this.form.get('country')?.value)
          this.selectDrpdValue(this.form.get('country')?.value, countriesData.find(country => country.iso_code2 === this.form.get('country')?.value)?.name_ru || '', 'country');

         if(this.form.get('dpGoal')?.value)
          this.selectDrpdValue(this.form.get('dpGoal')?.value, this.form.get('dpGoal')?.value, 'dpGoal');

         if(this.form.get('education')?.value)
          this.selectDrpdValue(this.form.get('education')?.value, this.form.get('education')?.value, 'education');

          this.profileService.getStatuses().subscribe(
            statuses => {
              this.statuses = statuses;
              if(this.form.get('statuses')?.value[0])
                this.selectDrpdValue(this.form.get('statuses')?.value[0], this.statuses.find((status: any) => status.value === this.form.get('statuses')?.value[0])?.label || '', 'statuses')
            }
          );

      },
      error: () => {
        // Handle error silently or add proper error handling
      }
    })

  }

  uploadAvatar(e: Event) {
    const files = (e.target as HTMLInputElement).files!
    this.fileService.upload(files, 'avatar').subscribe((res: any) => this.form.value.avatar = res[0])
  }

  saveForm() {
    if (!this.isProfile) {
      this.questionnaireSentState = true;
    } else {
      // Convert display dates back to ISO format before sending to backend
      const formData = { ...this.form.value };

      if (formData.birthDate) {
        const isoDate = this.parseDateFromDisplay(formData.birthDate);
        formData.birthDate = isoDate;
      }

      if (formData.dpDate) {
        const isoDate = this.parseDateFromDisplay(formData.dpDate);
        formData.dpDate = isoDate;
      }

      // Create a temporary form with ISO dates for submission
      const tempForm = this.fb.group(formData);

      this.profileService.update(tempForm).subscribe({
      next: () => {
        this.toasterService.showToast('Данные успешно сохранены.', 'success', 'bottom-middle', 3000);
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })

    }
  }

  toAuthPage() {
    this.router.navigate(['/ru/signin'], {queryParams: {return: '/ru/anketa'}});
  }

  selectDrpdValue(value: string, visibleValue: string, formName: string): void {
    this.form.get(formName).patchValue(value);
    this.dropdownOpenStates[formName] = false;
    this.dropdownSelectedValues[formName] = visibleValue;
  }

  toggleDrpd(key: string) {
    this.dropdownOpenStates[key] = !this.dropdownOpenStates[key];
  }

  protected readonly environment = environment;

  countries = countriesData;

  goToPrevForm(): void {
    const currentIndex = this.formsSections.indexOf(this.selectedMobileForm);
    if (currentIndex > 0) {
      this.selectedMobileForm = this.formsSections[currentIndex - 1];
      this.scrollToPosition();
    }
  }
  
  goToNextForm(): void {
    const currentIndex = this.formsSections.indexOf(this.selectedMobileForm);
    if (currentIndex < this.formsSections.length - 1) {
      this.selectedMobileForm = this.formsSections[currentIndex + 1];
      this.scrollToPosition();
    }
  }
  
  private scrollToPosition(): void {
    const scrollPosition = window.innerWidth > 768 ? 400 : 200;
    window.scrollTo({ top: scrollPosition, behavior: 'smooth' });
  }

  // Date utility functions for consistent formatting
  private formatDateForDisplay(isoDate: string | null): string {
    if (!isoDate) return '';
    try {
      const date = new Date(isoDate);
      if (isNaN(date.getTime())) return '';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear().toString();

      return `${day}/${month}/${year}`;
    } catch {
      return '';
    }
  }

  private parseDateFromDisplay(displayDate: string): string | null {
    if (!displayDate || displayDate.length !== 10) return null;

    const parts = displayDate.split('/');
    if (parts.length !== 3) return null;

    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10);
    const year = parseInt(parts[2], 10);

    if (isNaN(day) || isNaN(month) || isNaN(year)) return null;
    if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > 2100) return null;

    const date = new Date(year, month - 1, day);
    if (date.getDate() !== day || date.getMonth() !== month - 1 || date.getFullYear() !== year) {
      return null; // Invalid date (e.g., 31/02/2023)
    }

    // Additional validation for birth dates - shouldn't be in the future
    const today = new Date();
    if (date > today) {
      return null;
    }

    return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
  }

  private formatInputAsUserTypes(input: string): string {
    // Remove all non-numeric characters
    const numbers = input.replace(/\D/g, '');

    // Limit to 8 digits (DDMMYYYY)
    const limitedNumbers = numbers.slice(0, 8);

    // Format as DD/MM/YYYY
    if (limitedNumbers.length <= 2) {
      return limitedNumbers;
    } else if (limitedNumbers.length <= 4) {
      return `${limitedNumbers.slice(0, 2)}/${limitedNumbers.slice(2)}`;
    } else {
      return `${limitedNumbers.slice(0, 2)}/${limitedNumbers.slice(2, 4)}/${limitedNumbers.slice(4, 8)}`;
    }
  }

  private validateDateInput(input: string): boolean {
    if (!input) return true; // Empty is valid
    if (input.length !== 10) return false;
    return this.parseDateFromDisplay(input) !== null;
  }

  onDateInput(event: Event, controlName: string): void {
    const input = event.target as HTMLInputElement;
    const cursorPosition = input.selectionStart || 0;
    const oldValue = input.value;
    const newValue = this.formatInputAsUserTypes(input.value);

    input.value = newValue;

    // Adjust cursor position after formatting
    let newCursorPosition = cursorPosition;
    if (newValue.length > oldValue.length && (newValue[cursorPosition] === '/')) {
      newCursorPosition = cursorPosition + 1;
    }

    input.setSelectionRange(newCursorPosition, newCursorPosition);

    // Update form control with formatted value
    this.form.get(controlName)?.setValue(newValue, { emitEvent: false });
  }

  onDateKeyDown(event: KeyboardEvent): void {
    const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

    // Allow: backspace, delete, tab, escape, enter and arrow keys
    if (allowedKeys.includes(event.key)) {
      return;
    }

    // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
    if (event.ctrlKey && ['a', 'c', 'v', 'x'].includes(event.key.toLowerCase())) {
      return;
    }

    // Ensure that it is a number and stop the keypress
    if (!/[0-9]/.test(event.key)) {
      event.preventDefault();
    }
  }

  onDateBlur(event: Event, controlName: string): void {
    const input = event.target as HTMLInputElement;
    const displayValue = input.value;

    if (this.validateDateInput(displayValue)) {
      this.form.get(controlName)?.setErrors(null);
    } else if (displayValue) {
      this.form.get(controlName)?.setErrors({ invalidDate: true });
    }
  }

  getDateDisplayValue(controlName: string): string {
    const value = this.form.get(controlName)?.value;
    if (!value) return '';

    // If value is already in display format, return as is
    if (value.includes('/') && value.length === 10) {
      return value;
    }

    // If value is in ISO format, convert to display format
    return this.formatDateForDisplay(value);
  }
}
